import React, { useState, useRef } from 'react';
import BreadCrumb from '@/components/BreadCrumb';
import CommonSteps from '@/components/CommonSteps';
import RulesConfiguration from '../components/RulesConfiguration';
import DeviceSelection from '@/components/DeviceSelection';
import { isEmpty } from '@/utils/utils';
import { message, Modal } from 'antd';
import { DeviceChoiceTypeMap, DeviceEditFormConfig } from '../utils/constants';
import ConfigTaskManageApi from '@/fetch/bussiness/configTaskManage';
import ResultFeedback from '@/components/ResultFeedback';
import { HttpStatusCode } from '@/fetch/core/constant';
import { useNavigate } from 'react-router-dom';

const configTaskManageApi = new ConfigTaskManageApi();

const AddConfigTask = () => {
  const navigate = useNavigate();
  const [deviceChoiceType, setDeviceChoiceType] = useState<number>(
    DeviceChoiceTypeMap.DIRECTIONAL,
  );
  const [isTaskAddSuccess, setIsTaskAddSuccess] = useState<boolean>(false);
  const deviceSelectionRef = useRef<any>(null);
  const selectedDeviceRef = useRef<any>(null);
  const rulesConfigRef = useRef<any>(null);
  const breadCrumbList = [
    {
      title: '通用设备管理',
      route: '',
    },
    {
      title: '配置任务管理',
      route: '/ota/configTaskManage',
    },
    {
      title: '新建任务',
      route: '',
    },
  ];

  const updateSelectDevice = (selectRowKeys: any[], total?: number) => {
    if (deviceChoiceType === DeviceChoiceTypeMap.CONDITIONAL) {
      selectedDeviceRef.current = new Array(total);
    } else {
      selectedDeviceRef.current = selectRowKeys;
    }
  };

  const updateDeviceChoiceType = (value: any) => {
    setDeviceChoiceType(value);
  };

  const handleDeviceSelection = () => {
    return true;
    const searchValue = deviceSelectionRef.current?.getConditionFormValues();
    if (!searchValue.productKey || isEmpty(searchValue.productModelNoList)) {
      message.error('请选择产品和型号');
      return false;
    }
    if (deviceChoiceType === DeviceChoiceTypeMap.CONDITIONAL) {
      const flag = searchValue?.hasSearched;
      if (!flag) {
        message.error('请先搜索设备');
      }
      return flag;
    } else if (deviceChoiceType === DeviceChoiceTypeMap.DIRECTIONAL) {
      console.log('selectedDeviceRef.current', selectedDeviceRef.current);
      if (isEmpty(selectedDeviceRef.current)) {
        message.error('请选择设备');
      }
      return selectedDeviceRef.current?.length > 0;
    } else if (deviceChoiceType === DeviceChoiceTypeMap.UPLOAD) {
      const result = deviceSelectionRef.current?.getUploadResult();
      if (result?.success) {
        selectedDeviceRef.current = new Array(result?.total);
      }
      return result?.success;
    }
    return false;
  };

  const handleValidateRulesConfig = async () => {
    try {
      const res = await rulesConfigRef.current?.validateFormValues();
      return res;
    } catch (e) {
      return false;
    }
  };

  const handleStepsSubmit = async (cb: any) => {
    cb();
    setIsTaskAddSuccess(false);
    // console.log('rulesConfigRef.current', rulesConfigRef.current);
    // const rulesConfigData = await handleValidateRulesConfig();
    // if (!rulesConfigData) {
    //   return;
    // }
    // Modal.confirm({
    //   content: `确定推送${selectedDeviceRef.current?.length}台设备的配置下发？`,
    //   onOk: async () => {
    //     const rulesData = rulesConfigRef.current.getFormValues();
    //     const deviceChoiceInfo: any = {
    //       deviceChoiceType: deviceChoiceType,
    //     };
    //     if (deviceChoiceType === DeviceChoiceTypeMap.DIRECTIONAL) {
    //       deviceChoiceInfo.deviceNameList = selectedDeviceRef.current;
    //     } else if (deviceChoiceType === DeviceChoiceTypeMap.UPLOAD) {
    //       const result = deviceSelectionRef.current.getUploadResult();
    //       if (result?.success) {
    //         deviceChoiceInfo.deviceNameFileS3Key = result?.deviceNameFileS3Key;
    //         deviceChoiceInfo.deviceNameFileS3BucketName =
    //           result?.deviceNameFileS3BucketName;
    //         deviceChoiceInfo.deviceNameFileMd5 = result?.deviceNameFileMd5;
    //       }
    //     } else if (deviceChoiceType === DeviceChoiceTypeMap.CONDITIONAL) {
    //       const result = deviceSelectionRef.current.getConditionFormValues();
    //       deviceChoiceInfo.productKey = result?.productKey;
    //       deviceChoiceInfo.productModelNoList = result?.productModelNoList;
    //       deviceChoiceInfo.groupNoList = result?.groupNoList;
    //       deviceChoiceInfo.appType = result?.appType;
    //       deviceChoiceInfo.appName = result?.appName;
    //       deviceChoiceInfo.appVersionNumber = result?.appVersionNumber;
    //       deviceChoiceInfo.deviceName = result?.deviceName;
    //     }
    //     const requestData = {
    //       ...rulesData,
    //       deviceChoiceInfo: deviceChoiceInfo,
    //     };
    //     try {
    //       const res = await configTaskManageApi.createDeviceConfigIssueTask(
    //         requestData,
    //       );
    //       if (res.code === HttpStatusCode.Success) {
    //         cb();
    //         setIsTaskAddSuccess(res?.data || false);
    //         return true;
    //       } else {
    //         message.error(res?.message || '创建任务失败');
    //         return false;
    //       }
    //     } catch (err) {
    //       message.error('创建任务失败');
    //       return false;
    //     }
    //   },
    // });
  };
  const resultButton = [
    {
      text: '查看任务详情',
      clickFunc: () => {
        navigate('/');
      },
      showScope: ['success'],
    },
    {
      text: '返回',
      clickFunc: () => {
        navigate('/configTaskManage');
      },
      showScope: ['success', 'fail'],
    },
  ];

  const stepsTitleList = ['选择配置下发范围', '配置下发规则', '确认配置下发'];
  const stepsChildrenList = [
    <DeviceSelection
      ref={deviceSelectionRef}
      deviceChoiceType={deviceChoiceType}
      updateDeviceChoiceType={updateDeviceChoiceType}
      updateSelectDevice={updateSelectDevice}
      extraEditFormConfig={DeviceEditFormConfig}
      needCheckDeviceDetail
      hideSearchFormConfig
    />,
    <RulesConfiguration ref={rulesConfigRef} />,
    <ResultFeedback
      result={isTaskAddSuccess}
      buttonConfig={resultButton}
      resultMessage={{
        success: '成功创建配置下发任务',
        fail: '任务下发失败',
      }}
    />,
  ];
  const onNextCheckList = [handleDeviceSelection];

  return (
    <>
      <BreadCrumb items={breadCrumbList} />
      <CommonSteps
        stepTipList={stepsTitleList}
        children={stepsChildrenList}
        onNextCheckList={onNextCheckList}
        onSubmit={handleStepsSubmit}
      />
    </>
  );
};

export default AddConfigTask;
