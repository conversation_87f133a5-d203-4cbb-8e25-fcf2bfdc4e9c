import React from 'react';
import { Navigate, RouteObject } from 'react-router-dom';
import MainLayout from '@/layout/MainLayout';

// 使用 React.lazy 直接导入组件，并添加 webpackChunkName 注释
const ConfigManagement = React.lazy(
  () =>
    import(
      /* webpackChunkName: "ConfigManagement" */ '@/views/ConfigManagement'
    ),
);
const ConfigEditAndAdd = React.lazy(
  () =>
    import(
      /* webpackChunkName: "ConfigEditAndAdd" */ '@/views/ConfigManagement/EditAndAdd'
    ),
);
const VehicleConfigManage = React.lazy(
  () =>
    import(
      /* webpackChunkName: "VehicleConfigManage" */ '@/views/VehicleConfigManage'
    ),
);
const EditAndAddVehicle = React.lazy(
  () =>
    import(
      /* webpackChunkName: "EditAndAddVehicle" */ '@/views/VehicleConfigManage/EditAndAddVehicle'
    ),
);
const VehicleTypeManage = React.lazy(
  () =>
    import(
      /* webpackChunkName: "VehicleTypeManage" */ '@/views/VehicleTypeManage'
    ),
);
const VehicleTypeConfig = React.lazy(
  () =>
    import(
      /* webpackChunkName: "VehicleTypeConfig" */ '@/views/VehicleTypeManage/VehicleTypeConfig'
    ),
);
const VehicleTypeHandleHistory = React.lazy(
  () =>
    import(
      /* webpackChunkName: "VehicleTypeHandleHistory" */ '@/views/VehicleTypeManage/VehicleTypeHandleHistory'
    ),
);
const VehicleConfigAndRelease = React.lazy(
  () =>
    import(
      /* webpackChunkName: "VehicleConfigAndRelease" */ '@/views/VehicleConfigAndRelease'
    ),
);
const ConfRecord = React.lazy(
  () =>
    import(
      /* webpackChunkName: "ConfRecord" */ '@/views/VehicleConfigAndRelease/ConfRecord'
    ),
);
const PublishRecord = React.lazy(
  () =>
    import(
      /* webpackChunkName: "PublishRecord" */ '@/views/VehicleConfigAndRelease/PublishRecord'
    ),
);
const ReleaseConfSoftware = React.lazy(
  () =>
    import(
      /* webpackChunkName: "ReleaseConfSoftware" */ '@/views/VehicleConfigAndRelease/ReleaseConfSoftware'
    ),
);
const VehicleConf = React.lazy(
  () =>
    import(
      /* webpackChunkName: "VehicleConf" */ '@/views/VehicleConfigAndRelease/VehicleConf'
    ),
);
const ProductManage = React.lazy(
  () => import(/* webpackChunkName: "ProductManage" */ '@/views/ProductManage'),
);
const AddProduct = React.lazy(
  () =>
    import(
      /* webpackChunkName: "AddProduct" */ '@/views/ProductManage/AddProduct'
    ),
);
const TSLModel = React.lazy(
  () =>
    import(/* webpackChunkName: "TSLModel" */ '@/views/ProductManage/TSLModel'),
);
const TSLDraft = React.lazy(
  () =>
    import(/* webpackChunkName: "TSLDraft" */ '@/views/ProductManage/TSLDraft'),
);
const EditAddTSL = React.lazy(
  () =>
    import(
      /* webpackChunkName: "EditAddTSL" */ '@/views/ProductManage/EditAddTSL'
    ),
);
const ProductDetail = React.lazy(
  () =>
    import(
      /* webpackChunkName: "ProductDetail" */ '@/views/ProductManage/ProductDetail'
    ),
);
const DeviceManage = React.lazy(
  () => import(/* webpackChunkName: "DeviceManage" */ '@/views/DeviceManage'),
);
const GroupManage = React.lazy(
  () => import(/* webpackChunkName: "GroupManage" */ '@/views/GroupManage'),
);
const ReleasePlanManage = React.lazy(
  () =>
    import(
      /* webpackChunkName: "ReleasePlanManage" */ '@/views/ReleasePlanManage'
    ),
);
const CheckConfSoftware = React.lazy(
  () =>
    import(
      /* webpackChunkName: "CheckConfSoftware" */ '@/views/ReleasePlanManage/CheckConfSoftware'
    ),
);
const ReleasePlan = React.lazy(
  () => import(/* webpackChunkName: "ReleasePlan" */ '@/views/ReleasePlan'),
);
const CreateReleasePlan = React.lazy(
  () =>
    import(
      /* webpackChunkName: "CreateReleasePlan" */ '@/views/ReleasePlan/Create'
    ),
);
const ReleasePlanDetail = React.lazy(
  () =>
    import(
      /* webpackChunkName: "ReleasePlanDetail" */ '@/views/ReleasePlan/Detail'
    ),
);
const HardwareSerialManage = React.lazy(
  () =>
    import(
      /* webpackChunkName: "HardwareSerialManage" */ '@/views/HardwareSerialManage'
    ),
);
const UpgradePackageManagement = React.lazy(
  () =>
    import(
      /* webpackChunkName: "UpgradePackageManagement" */ '@/views/UpgradePackageManagement'
    ),
);
const NoPermission = React.lazy(
  () => import(/* webpackChunkName: "NoPermission" */ '@/views/NoPermission'),
);
const CommandControl = React.lazy(
  () =>
    import(/* webpackChunkName: "CommandControl" */ '@/views/CommandControl'),
);
const AddTask = React.lazy(
  () =>
    import(/* webpackChunkName: "AddTask" */ '@/views/CommandControl/AddTask'),
);
const CheckTask = React.lazy(
  () =>
    import(
      /* webpackChunkName: "CheckTask" */ '@/views/CommandControl/CheckTask'
    ),
);
const Firmware = React.lazy(
  () => import(/* webpackChunkName: "Firmware" */ '@/views/Firmware'),
);
const FirmwareInfo = React.lazy(
  () =>
    import(
      /* webpackChunkName: "FirmwareInfo" */ '@/views/Firmware/FirmwareInfo'
    ),
);
const AddPackage = React.lazy(
  () =>
    import(/* webpackChunkName: "AddPackage" */ '@/views/Firmware/AddPackage'),
);
const PushDetail = React.lazy(
  () =>
    import(/* webpackChunkName: "PushDetail" */ '@/views/Firmware/PushDetail'),
);

const ConfManagement = React.lazy(
  () =>
    import(/* webpackChunkName: "ConfManagement" */ '@/views/ConfManagement'),
);
const ConfigTemplateManage = React.lazy(
  () =>
    import(
      /* webpackChunkName: "ConfigTemplateManage" */ '@/views/ConfigTemplateManage'
    ),
);
const AddConfigTemplate = React.lazy(
  () =>
    import(
      /* webpackChunkName: "AddConfigTemplate" */ '@/views/ConfigTemplateManage/addConfigTemplate'
    ),
);
const ConfigTaskManage = React.lazy(
  () =>
    import(
      /* webpackChunkName: "ConfigTaskManage" */ '@/views/ConfigTaskManage'
    ),
);

const AddConfigTask = React.lazy(
  () =>
    import(
      /* webpackChunkName: "AddConfigTask" */ '@/views/ConfigTaskManage/AddConfigTask'
    ),
);

// 定义路由类型
type RouteItem = RouteObject;
type RouteConfig = RouteObject;

// 分组路由
const configRoutes: RouteItem[] = [
  { index: true, element: <ConfigManagement /> },
  { path: 'edit', element: <ConfigEditAndAdd /> },
  { path: 'add', element: <ConfigEditAndAdd /> },
];

const vehicleTypeRoutes: RouteItem[] = [
  { index: true, element: <VehicleTypeManage /> },
  { path: 'vehicleTypeConfig', element: <VehicleTypeConfig /> },
  { path: 'vehicleTypeHandleHistory', element: <VehicleTypeHandleHistory /> },
];

const vehicleConfigRoutes: RouteItem[] = [
  { index: true, element: <VehicleConfigManage /> },
  { path: 'edit', element: <EditAndAddVehicle /> },
  { path: 'add', element: <EditAndAddVehicle /> },
];

const vehicleConfigAndReleaseRoutes: RouteItem[] = [
  { index: true, element: <VehicleConfigAndRelease /> },
  { path: 'confRecord', element: <ConfRecord /> },
  { path: 'publishRecord', element: <PublishRecord /> },
  { path: 'releaseConfSoftware', element: <ReleaseConfSoftware /> },
  { path: 'vehicleConf', element: <VehicleConf /> },
];

const releasePlanManageRoutes: RouteItem[] = [
  { index: true, element: <ReleasePlanManage /> },
  { path: 'checkConfSoftware', element: <CheckConfSoftware /> },
];

const productRoutes: RouteItem[] = [
  { index: true, element: <ProductManage /> },
  { path: 'AddProduct', element: <AddProduct /> },
  { path: 'TSLDraft', element: <TSLDraft /> },
  { path: 'TSLModel', element: <TSLModel /> },
  { path: 'EditAddTSL', element: <EditAddTSL /> },
  { path: 'ProductDetail', element: <ProductDetail /> },
];

const commandControlRoutes: RouteItem[] = [
  { index: true, element: <CommandControl /> },
  { path: 'AddTask', element: <AddTask /> },
  { path: 'CheckTask', element: <CheckTask /> },
];

const firmwareRoutes: RouteItem[] = [
  { index: true, element: <Firmware pageType="firmware" /> },
  { path: 'firmwareInfo', element: <FirmwareInfo /> },
  { path: 'addPackage', element: <AddPackage /> },
  { path: 'pushDetail', element: <PushDetail /> },
];

const appRoutes: RouteItem[] = [
  { index: true, element: <Firmware pageType="app" /> },
  { path: 'firmwareInfo', element: <FirmwareInfo /> },
  { path: 'addPackage', element: <AddPackage /> },
  { path: 'pushDetail', element: <PushDetail /> },
];

const releasePlanRoutes: RouteItem[] = [
  { index: true, element: <ReleasePlan /> },
  { path: 'create', element: <CreateReleasePlan /> },
  { path: 'detail', element: <ReleasePlanDetail /> },
];

const deviceConfManage: RouteItem[] = [
  { index: true, element: <ConfManagement /> },
];
// 配置模板管理路由配置
const configTemplateRoutes: RouteItem[] = [
  { index: true, element: <ConfigTemplateManage /> },
  { path: 'add', element: <AddConfigTemplate /> },
  // { path: 'copy/:templateNo', element: <ConfigTemplateEdit /> },
  // { path: 'deviceConfig/:templateNo', element: <DeviceConfig /> },
  // { path: 'initRecord/:templateNo', element: <InitRecord /> },
];
const configTaskManageRoutes: RouteItem[] = [
  { index: true, element: <ConfigTaskManage /> },
  { path: 'add', element: <AddConfigTask /> },
];

/**
 * 主路由配置
 *
 * 路由组织为层次结构：
 * - 根路由('/') 使用 MainLayout 作为元素，并有子路由
 * - 应用程序的每个部分都有自己的路由和子路由
 * - 子路由被定义在单独的数组中，以便更好地组织
 */
const routes: RouteConfig[] = [
  {
    path: '/',
    element: <MainLayout />,
    children: [
      { index: true, element: <Navigate to="configManagement" /> },
      { path: '404', element: <NoPermission /> },
    ],
  },
  { path: 'configManagement', children: configRoutes },
  { path: 'vehicleTypeManage', children: vehicleTypeRoutes },
  { path: 'vehicleConfigManage', children: vehicleConfigRoutes },
  { path: 'vehicleConfigAndRelease', children: vehicleConfigAndReleaseRoutes },
  { path: 'releasePlanManage', children: releasePlanManageRoutes },
  {
    path: 'hardwareSerialManage',
    children: [{ index: true, element: <HardwareSerialManage /> }],
  },
  {
    path: 'upgrademanagement',
    children: [{ index: true, element: <UpgradePackageManagement /> }],
  },
  { path: 'device', children: [{ index: true, element: <DeviceManage /> }] },
  { path: 'product', children: productRoutes },
  { path: 'group', children: [{ index: true, element: <GroupManage /> }] },
  { path: 'commandControl', children: commandControlRoutes },
  { path: 'firmware', children: firmwareRoutes },
  { path: 'app', children: appRoutes },
  { path: 'releasePlan', children: releasePlanRoutes },
  {
    path: 'confManagement',
    children: deviceConfManage,
  },
  {
    path: 'configTemplate',
    children: configTemplateRoutes,
  },
  {
    path: 'configTaskManage',
    children: configTaskManageRoutes,
  },
];

// 导出路由
export default routes;
