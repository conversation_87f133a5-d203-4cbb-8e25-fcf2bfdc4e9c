import React from 'react';
import { CloseCircleFilled, CheckCircleFilled } from '@ant-design/icons';

type resultType = 'success' | 'fail';
interface ButtonConfig {
  text: string;
  clickFunc: Function;
  showScope?: resultType[];
}
interface ResultMessage {
  success: string;
  fail: string;
}

const ResultFeedback = (props: {
  result?: boolean;
  buttonConfig: ButtonConfig[];
  resultMessage: string | ResultMessage;
}) => {
  const { result = true, buttonConfig, resultMessage } = props;
  return (
    <div
      style={{
        textAlign: 'center',
        fontSize: '14px',
        lineHeight: '30px',
      }}
    >
      {result ? (
        <CheckCircleFilled style={{ color: '#52c41a', fontSize: '40px' }} />
      ) : (
        <CloseCircleFilled style={{ color: 'red', fontSize: '40px' }} />
      )}
      <div>
        {typeof resultMessage === 'string'
          ? resultMessage
          : result
          ? resultMessage.success
          : resultMessage.fail}
      </div>
      <div style={{ display: 'flex', justifyContent: 'center', gap: '10px' }}>
        {buttonConfig?.map((item: ButtonConfig, index: number) => {
          if (item?.showScope && !item?.showScope.includes(result)) {
            return null;
          }
          return (
            <a
              style={{ color: '#1677ff', cursor: 'pointer' }}
              key={`btn-${index}`}
              onClick={() => {
                item?.clickFunc();
              }}
            >
              {item?.text}
            </a>
          );
        })}
      </div>
    </div>
  );
};

export default ResultFeedback;
